"use client";

import Link from "next/link";
import { useTranslations, useLocale } from 'next-intl';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Coffee, Package, Gift, Star, ArrowRight, Truck } from "lucide-react";

export default function Home() {
  const t = useTranslations();
  const locale = useLocale();

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative bg-gradient-hero py-20 lg:py-32 overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-primary/20 to-transparent rounded-full animate-float"></div>
          <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-secondary/20 to-transparent rounded-full animate-float" style={{animationDelay: '1s'}}></div>
          <div className="absolute bottom-20 left-1/4 w-20 h-20 bg-gradient-to-br from-accent/20 to-transparent rounded-full animate-float" style={{animationDelay: '2s'}}></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center animate-fade-in-up">
            <h1 className="text-4xl lg:text-6xl font-bold tracking-tight mb-6">
              <span className="text-foreground">
                {t('homepage.hero.title')}
              </span>
              <span className="text-primary block">
                {t('homepage.hero.subtitle')}
              </span>
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto animate-fade-in-up" style={{animationDelay: '0.2s'}}>
              {t('homepage.hero.description')}
            </p>

            {/* Primary CTA - Coffee Box Builder */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12 animate-fade-in-up" style={{animationDelay: '0.4s'}}>
              <Button size="lg" className="text-lg px-8 py-6 h-auto shadow-glow hover:shadow-glow-hover group" asChild>
                <Link href={`/${locale}/coffee-box-builder`}>
                  <Package className="mr-2 h-5 w-5 group-hover:rotate-12 transition-transform-smooth" />
                  {t('homepage.hero.ctaPrimary')}
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform-smooth" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" className="text-lg px-8 py-6 h-auto group" asChild>
                <Link href={`/${locale}/shop`}>
                  <span className="group-hover:bg-gradient-to-r group-hover:from-primary group-hover:to-primary/80 group-hover:bg-clip-text group-hover:text-transparent transition-all-smooth">
                    {t('homepage.hero.ctaSecondary')}
                  </span>
                </Link>
              </Button>
            </div>

            {/* Trust indicators */}
            <div className="flex flex-wrap justify-center items-center gap-8 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <Truck className="h-4 w-4" />
                <span>{t('homepage.trustIndicators.freeShipping')}</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 fill-current" />
                <span>{t('homepage.trustIndicators.premiumQuality')}</span>
              </div>
              <div className="flex items-center gap-2">
                <Gift className="h-4 w-4" />
                <span>{t('homepage.trustIndicators.gifts')}</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gradient-to-br from-muted/30 via-background to-muted/20 relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-10 right-10 w-40 h-40 bg-gradient-to-br from-primary/10 to-transparent rounded-full animate-float" style={{animationDelay: '0.5s'}}></div>
          <div className="absolute bottom-10 left-10 w-32 h-32 bg-gradient-to-br from-secondary/10 to-transparent rounded-full animate-float" style={{animationDelay: '1.5s'}}></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-16 animate-fade-in-up">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4 text-foreground">
              {t('homepage.features.title')}
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              {t('homepage.features.subtitle')}
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card className="text-center group hover-glow animate-fade-in-up" style={{animationDelay: '0.1s'}}>
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform-smooth shadow-soft group-hover:shadow-glow">
                  <Package className="h-6 w-6 text-primary group-hover:rotate-12 transition-transform-smooth" />
                </div>
                <CardTitle className="group-hover:text-primary transition-colors">{t('homepage.features.coffeeBoxBuilder.title')}</CardTitle>
                <CardDescription>
                  {t('homepage.features.coffeeBoxBuilder.description')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  variant="outline"
                  className="text-foreground hover:bg-gradient-primary hover:text-foreground hover:border-primary"
                  asChild
                >
                  <Link href={`/${locale}/coffee-box-builder`}>{t('homepage.features.coffeeBoxBuilder.cta')}</Link>
                </Button>
              </CardContent>
            </Card>

            <Card className="text-center group hover-glow animate-fade-in-up" style={{animationDelay: '0.2s'}}>
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform-smooth shadow-soft group-hover:shadow-glow">
                  <Gift className="h-6 w-6 text-primary group-hover:rotate-12 transition-transform-smooth" />
                </div>
                <CardTitle className="group-hover:text-primary transition-colors">{t('homepage.features.bundles.title')}</CardTitle>
                <CardDescription>
                  {t('homepage.features.bundles.description')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  variant="outline"
                  className="text-foreground hover:bg-gradient-primary hover:text-foreground hover:border-primary"
                  asChild
                >
                  <Link href={`/${locale}/bundles`}>{t('homepage.features.bundles.cta')}</Link>
                </Button>
              </CardContent>
            </Card>

            <Card className="text-center group hover-glow animate-fade-in-up" style={{animationDelay: '0.3s'}}>
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform-smooth shadow-soft group-hover:shadow-glow">
                  <Coffee className="h-6 w-6 text-primary group-hover:rotate-12 transition-transform-smooth" />
                </div>
                <CardTitle className="group-hover:text-primary transition-colors">{t('homepage.features.shop.title')}</CardTitle>
                <CardDescription>
                  {t('homepage.features.shop.description')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  variant="outline"
                  className="text-foreground hover:bg-gradient-primary hover:text-foreground hover:border-primary"
                  asChild
                >
                  <Link href={`/${locale}/shop`}>{t('homepage.features.shop.cta')}</Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-primary via-primary/95 to-primary/90 text-primary-foreground relative overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-10 left-1/4 w-24 h-24 bg-white/10 rounded-full animate-float"></div>
          <div className="absolute bottom-10 right-1/4 w-32 h-32 bg-white/5 rounded-full animate-float" style={{animationDelay: '1s'}}></div>
          <div className="absolute top-1/2 left-10 w-16 h-16 bg-white/15 rounded-full animate-float" style={{animationDelay: '2s'}}></div>
        </div>

        <div className="container mx-auto px-4 text-center relative z-10">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4 animate-fade-in-up">
            {t('homepage.finalCta.title')}
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto animate-fade-in-up" style={{animationDelay: '0.1s'}}>
            {t('homepage.finalCta.description')}
          </p>
          <Button size="lg" variant="secondary" className="text-lg px-8 py-6 h-auto shadow-strong hover:shadow-glow-hover hover:scale-105 group animate-fade-in-up" style={{animationDelay: '0.2s'}} asChild>
            <Link href={`/${locale}/coffee-box-builder`}>
              <Package className="mr-2 h-5 w-5 group-hover:rotate-12 transition-transform-smooth" />
              {t('homepage.finalCta.cta')}
            </Link>
          </Button>
        </div>
      </section>
    </div>
  );
}
