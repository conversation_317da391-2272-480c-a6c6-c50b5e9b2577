import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all-smooth focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 relative overflow-hidden",
  {
    variants: {
      variant: {
        default:
          "bg-gradient-to-r from-primary to-primary/90 text-primary-foreground shadow-medium hover:shadow-glow hover:scale-105 hover:from-primary/90 hover:to-primary/80 active:scale-95 transition-all duration-300",
        luxury:
          "bg-gradient-luxury text-primary-foreground shadow-strong hover:shadow-glow-hover hover:scale-105 active:scale-95 transition-all duration-300 relative before:absolute before:inset-0 before:bg-gradient-to-r before:from-secondary/20 before:to-transparent before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300",
        destructive:
          "bg-gradient-to-r from-destructive to-destructive/90 text-destructive-foreground shadow-medium hover:shadow-strong hover:scale-105 hover:from-destructive/90 hover:to-destructive/80 active:scale-95 transition-all duration-300",
        outline:
          "border border-input bg-gradient-card shadow-soft hover:bg-gradient-to-r hover:from-accent/50 hover:to-accent hover:text-accent-foreground hover:shadow-medium hover:scale-105 active:scale-95 transition-all duration-300",
        secondary:
          "bg-gradient-secondary text-secondary-foreground shadow-soft hover:shadow-medium hover:scale-105 hover:opacity-90 active:scale-95 transition-all duration-300",
        ghost: "hover:bg-gradient-to-r hover:from-accent/30 hover:to-accent/50 hover:text-accent-foreground hover:scale-105 active:scale-95 transition-all duration-300",
        link: "text-primary underline-offset-4 hover:underline hover:scale-105 active:scale-95 transition-all duration-300",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = "Button";

export { Button, buttonVariants };
